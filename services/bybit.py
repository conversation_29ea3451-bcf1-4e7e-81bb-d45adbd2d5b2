import logging
import os
import sys
import traceback
from decimal import ROUND_HALF_UP

import requests
from typing import Any, Dict, Optional

from models.exchange_connection import ExchangeConnection
from services.api_utils import (
    api_call, BybitAPIError, APIErrorHandler
)

# Set Environment Variables
# setx BYBIT_ENABLE_SESSIONS "true"
# echo $env:BYBIT_ENABLE_SESSIONS

# setx JAPAN_PROXY_API_TOKEN "9x!W#2@f^P7zRk8sQyL0dTgVh3mZcB1nUoE*XaJ4lM"
# echo $env:JAPAN_PROXY_API_TOKEN

# setx JAPAN_PROXY_URL "http://***************:8000"
# echo $env:JAPAN_PROXY_URL

# Configure logger for Bybit API requests
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Other imports are done within methods to avoid circular imports
# Timeframe mapping for Bybit V5 intervals
_INTERVAL_MAP = {
    "1m": "1", "3m": "3", "5m": "5", "15m": "15", "30m": "30",
    "1h": "60", "2h": "120", "4h": "240", "6h": "360",
    # 8h not supported, skip
    "12h": "720", "1d": "D", "1w": "W", "1M": "M",
}


class Bybit:
    @staticmethod
    def get_api_url():
        """Get Japan proxy base URL from env with sensible default."""
        return os.getenv("JAPAN_PROXY_URL", "http://***************:8000")

    @staticmethod
    def get_headers():
        """Base headers including proxy token; user creds can be added per-request."""
        return {
            "x-api-token": os.getenv("JAPAN_PROXY_API_TOKEN", ""),
            "Content-Type": "application/json",
        }

    @staticmethod
    def _log_api_request(method, url, params=None, headers=None, response=None, error=None):
        """
        Log API request details for debugging and monitoring.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: Full URL of the request
            params: Request parameters
            headers: Request headers (sensitive data will be masked)
            response: Response object or data
            error: Exception if request failed
        """
        # Mask sensitive headers for logging
        safe_headers = {}
        if headers:
            for key, value in headers.items():
                if 'token' in key.lower() or 'auth' in key.lower():
                    safe_headers[key] = "***MASKED***"
                else:
                    safe_headers[key] = value

        log_data = {
            "method": method,
            "url": url,
            "params": params,
            "headers": safe_headers
        }

        if error:
            logger.error(f"Bybit API request failed: {log_data}, Error: {error}")
        else:
            status_code = getattr(response, 'status_code', 'N/A')
            response_size = len(str(response.text)) if hasattr(response, 'text') else 'N/A'
            logger.info(f"Bybit API request: {log_data}, Status: {status_code}, Response size: {response_size} chars")

    @staticmethod
    def getSession():
        raise NotImplementedError("Remote proxy does not use getSession().")

    @staticmethod
    def getExecutions():
        url = f"{Bybit.get_api_url()}/bybit/executions"
        headers = Bybit.get_headers()
        try:
            r = requests.get(url, headers=headers)
            Bybit._log_api_request("GET", url, headers=headers, response=r)
            r.raise_for_status()  # Raise an exception for bad status codes
            return r.json()
        except Exception as e:
            Bybit._log_api_request("GET", url, headers=headers, error=e)
            raise

    @staticmethod
    def getPrice(ticker):
        url = f"{Bybit.get_api_url()}/bybit/price"
        params = {"ticker": ticker}
        headers = Bybit.get_headers()
        try:
            r = requests.get(url, params=params, headers=headers)
            Bybit._log_api_request("GET", url, params=params, headers=headers, response=r)
            r.raise_for_status()
            return float(r.json())
        except Exception as e:
            Bybit._log_api_request("GET", url, params=params, headers=headers, error=e)
            raise

    @staticmethod
    def get_current_open_positions(category: str = "linear", settleCoin: str | None = None,
                                   username: str | None = None):
        """
        Retrieves open positions from Bybit and creates Trade objects
        representing the current open positions.

        Args:
            category: The product category (e.g., "linear", "inverse", "spot"). Default is "linear".
            settleCoin: The settlement currency (e.g., "USDT", "USD", "BTC"). Optional.

        Returns:
            list: A list of Trade objects representing open positions
            :param username:
        """
        from models.bybit_position import BybitPosition
        from models.trade import Trade

        # Prepare parameters for the API call
        params = {"category": category}
        if settleCoin:
            params["settleCoin"] = settleCoin

        # Get positions data from API
        url = f"{Bybit.get_api_url()}/bybit/positions"
        headers = Bybit.get_headers()
        try:
            r = requests.get(url, params=params, headers=headers)
            Bybit._log_api_request("GET", url, params=params, headers=headers, response=r)
            r.raise_for_status()
            positions_data = r.json()
        except Exception as e:
            Bybit._log_api_request("GET", url, params=params, headers=headers, error=e)
            raise

        # Filter and convert to BybitPosition objects
        # Check for both "qty" and "size" fields to handle different API response formats
        positions = []
        for p in positions_data:
            # Check if position has size/qty > 0
            position_size = float(p.get("size", p.get("qty", 0)))
            if position_size > 0:
                try:
                    position = BybitPosition(p)
                    positions.append(position)
                except Exception as e:
                    traceback.print_exc()
                    print(f"Error creating BybitPosition from data: {e}")
                    print(f"Position data: {p}")
                    # Continue processing other positions even if one fails

        # Convert positions directly to trades
        trades = []
        for position in positions:
            try:
                # Create a Trade object directly from Bybit Position since we don't have opening order data
                trade = Trade.from_bybit_position(position, username=username)
                trades.append(trade)
            except Exception as e:
                print(f"❌ Error processing position {position.symbol}: {str(e)}", file=sys.stderr)
                traceback.print_exc()
                # Continue processing other positions even if one fails

        # Log summary
        print(f"Processed {len(positions)} positions into {len(trades)} trades")
        return trades

    @staticmethod
    def getOrders(start_ms=None, end_ms=None):
        from models.bybit_order import BybitOrder
        params = {}
        if start_ms: params["startTime"] = int(start_ms)
        if end_ms: params["endTime"] = int(end_ms)

        url = f"{Bybit.get_api_url()}/bybit/orders"
        headers = Bybit.get_headers()
        try:
            r = requests.get(url, params=params, headers=headers)
            Bybit._log_api_request("GET", url, params=params, headers=headers, response=r)
            r.raise_for_status()
            orders_list = [BybitOrder(order) for order in r.json()]
            return orders_list
        except Exception as e:
            Bybit._log_api_request("GET", url, params=params, headers=headers, error=e)
            raise

    @staticmethod
    def get_symbols():
        """Return list of Bybit linear (USDT) symbols via Japan proxy."""
        url = f"{Bybit.get_api_url()}/bybit/instruments"
        headers = Bybit.get_headers()
        try:
            r = requests.get(url, headers=headers, timeout=10)
            Bybit._log_api_request("GET", url, headers=headers, response=r)
            r.raise_for_status()
            data = r.json()
            items = (data.get("result", {}).get("list", []) or [])
            return [it.get("symbol") for it in items if it.get("symbol")]
        except Exception as e:
            Bybit._log_api_request("GET", url, headers=headers, error=e)
            raise

    @staticmethod
    def get_candlestick_dataframe(symbol: str, timeframe: str = "1h",
                                  limit: int = 500, start_ms: int | None = None, end_ms: int | None = None):
        """Fetch candles through Japan proxy and return a pandas DataFrame indexed by open_time (UTC)."""
        import pandas as pd
        interval = _INTERVAL_MAP[timeframe]
        url = f"{Bybit.get_api_url()}/bybit/kline"
        params = {"symbol": symbol.upper(), "interval": interval, "limit": min(max(1, limit), 1000)}
        if start_ms is not None: params["start"] = int(start_ms)
        if end_ms is not None: params["end"] = int(end_ms)
        headers = Bybit.get_headers()
        try:
            r = requests.get(url, params=params, headers=headers, timeout=10)
            Bybit._log_api_request("GET", url, params=params, headers=headers, response=r)
            r.raise_for_status()
            payload = r.json()
            rows = list(reversed((payload.get("result", {}) or {}).get("list", []) or []))
            # interval ms
            step_ms = {
                          "1": 60, "3": 180, "5": 300, "15": 900, "30": 1800,
                          "60": 3600, "120": 7200, "240": 14400, "360": 21600,
                          "720": 43200, "D": 86400, "W": 604800, "M": 2592000
                      }[interval] * 1000
            recs = []
            for it in rows:  # [startMs, open, high, low, close, volume, turnover]
                ts_ms = int(it[0])
                recs.append({
                    "open_time": pd.to_datetime(ts_ms, unit="ms", utc=True),
                    "open": float(it[1]), "high": float(it[2]),
                    "low": float(it[3]), "close": float(it[4]),
                    "volume": float(it[5]) if len(it) > 5 and it[5] is not None else 0.0,
                    "close_time": pd.to_datetime(ts_ms + step_ms, unit="ms", utc=True),
                })
            df = pd.DataFrame.from_records(recs)
            if not df.empty:
                df.set_index("open_time", inplace=True)
            return df
        except Exception as e:
            Bybit._log_api_request("GET", url, params=params, headers=headers, error=e)
            raise

    @staticmethod
    def getAccountBalance(connection: Optional[object] = None, keep_session: bool = False):
        """
        Get the account balance from Bybit as a formatted string.

        If an ExchangeConnection is provided, credentials are decrypted and a short-lived
        WebSocket monitoring session is started on the proxy. The balance request is made
        using the user's credentials. The session is stopped by default unless keep_session=True.

        Args:
            connection: ExchangeConnection containing api_key and encrypted secret
            keep_session: Whether to keep the WS session alive after the balance call

        Returns:
            str: Formatted account balance (e.g., "123.4567")
        """
        base_url = Bybit.get_api_url()
        base_headers = Bybit.get_headers()
        enable_sessions = os.getenv("BYBIT_ENABLE_SESSIONS", "false").lower() in ("1", "true", "yes")

        session_id = None
        try:
            if connection is not None and enable_sessions:
                # Start monitoring session using centralized function
                session_id = Bybit.start_monitoring_session(connection)

                # Set up headers with user credentials for balance request
                try:
                    from utils.encryption import decrypt_api_secret
                    api_secret = decrypt_api_secret(getattr(connection, 'api_secret_encrypted', ''))
                    api_key = getattr(connection, 'api_key', None)

                    headers = dict(base_headers)
                    headers.update({
                        "x-bybit-api-key": api_key,
                        "x-bybit-api-secret": api_secret,
                        "x-bybit-testnet": "true" if getattr(connection, 'is_testnet', False) else "false",
                    })
                except Exception as e:
                    raise BybitAPIError(f"Failed to decrypt Bybit API secret: {e}")
            else:
                # Backward-compat: attempt without explicit credentials
                headers = base_headers

            # Perform the balance request
            bal_url = f"{base_url}/bybit/balance"
            r_bal = requests.get(bal_url, headers=headers, timeout=15)
            Bybit._log_api_request("GET", bal_url, headers=headers, response=r_bal)
            r_bal.raise_for_status()
            equity = float(r_bal.json().get('equity'))
            return f"{equity:.4f}"

        except Exception as e:
            Bybit._log_api_request("GET", f"{base_url}/bybit/balance", headers=base_headers, error=e)
            raise
        finally:
            # Stop the session unless the caller wants to keep it
            if enable_sessions and session_id and not keep_session:
                Bybit.stop_monitoring_session(session_id)

    @staticmethod
    def getAccountBalanceDecimal():
        """
        Gets the account balance from Bybit as a Decimal value.

        Returns:
            Decimal: Account balance as a Decimal
        """
        from decimal import Decimal
        url = f"{Bybit.get_api_url()}/bybit/balance"
        headers = Bybit.get_headers()
        try:
            r = requests.get(url, headers=headers)
            Bybit._log_api_request("GET", url, headers=headers, response=r)
            r.raise_for_status()
            balance = Decimal(str(r.json()['equity']))
            return balance.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        except Exception as e:
            Bybit._log_api_request("GET", url, headers=headers, error=e)
            raise

    @staticmethod
    def get_bybit_orders(category: str = "linear", settleCoin: str | None = None, username: str | None = None):
        """
        Retrieves orders from Bybit and current open positions.

        Args:
            category: The product category (e.g., "linear", "inverse", "spot"). Default is "linear".
            settleCoin: The settlement currency (e.g., "USDT", "USD", "BTC"). Optional.

        Returns:
            tuple: (current_open_positions, orders, first_import) - Lists of current open positions and orders,
            and a boolean indicating if it's the first import
        """
        from datetime import datetime, timedelta
        import time
        from models.order import Order
        from models.trade import Exchange
        from trades_db import TradesDB
        import helper

        last_update = helper.date_to_ms(TradesDB.get_bybit_last_order_time(), 1000)
        current_open_positions = []
        if last_update:
            now_ms = int(time.time() * 1000)
            orders = Bybit.getOrders(start_ms=last_update + 1, end_ms=now_ms)

            # For non-first imports, we get open positions from the DB since we recreate oldest - newest now
            current_open_positions = TradesDB.get_open_trades_by_exchange(Exchange.BYBIT)
            # Sort current_open_positions by oldest first
            current_open_positions.sort(key=lambda t: t.timeOpen, reverse=False)
        else:
            # 2 years ago in milliseconds
            two_years_ago = datetime.utcnow() - timedelta(days=60)  # 365 * 2
            two_years_ms = int(two_years_ago.timestamp() * 1000)
            now_ms = int(time.time() * 1000)

            start_dt = datetime.utcfromtimestamp(two_years_ms / 1000)
            end_dt = datetime.utcfromtimestamp(now_ms / 1000)

            start_ms = int(start_dt.timestamp() * 1000)
            end_ms = int(end_dt.timestamp() * 1000)

            # Calculates the first 7-day chunk
            window_end_ms = min(start_ms + (7 * 24 * 60 * 60 * 1000), end_ms)
            Bybit.print_time_diff(start_ms, window_end_ms)
            orders = Bybit.getOrders(start_ms=start_ms, end_ms=now_ms)

            # For first import scenario we get live open positions, no open positions in DB (duh), also
            # Re-create trades first run is newest - oldest
            current_open_positions = Bybit.get_current_open_positions(category=category,
                                                                      settleCoin=settleCoin,
                                                                      username=username)

            # Sort current_open_positions by newest first
            current_open_positions.sort(key=lambda t: t.timeOpen, reverse=True)

        # Get the set of already processed order IDs
        processed_order_ids = set(TradesDB.getBybitOrderIds())

        # Filter out orders that have already been processed
        orders = [order for order in orders if order.bybit_order_id not in processed_order_ids]

        # Convert and return the new orders
        orders = [Order.from_bybit_order(order) for order in orders]

        # Sort orders chronologically by filled_date (or created_date if filled_date is None)
        # This ensures we process the oldest orders first for proper trade reconstruction
        def get_order_date(order):
            return order.filled_date if order.filled_date else order.created_date

        if last_update:
            sorted_orders = sorted(orders, key=get_order_date, reverse=False)
        else:
            sorted_orders = sorted(orders, key=get_order_date, reverse=True)

        return current_open_positions, sorted_orders, True if last_update is None else False

    @staticmethod
    def print_time_diff(start_ms, end_ms):
        """
        Prints the time difference between two timestamps in a human-readable format.

        Args:
            start_ms: Start time in milliseconds
            end_ms: End time in milliseconds
        """
        diff_ms = end_ms - start_ms

        days = diff_ms // (24 * 60 * 60 * 1000)
        diff_ms %= (24 * 60 * 60 * 1000)

        hours = diff_ms // (60 * 60 * 1000)
        diff_ms %= (60 * 60 * 1000)

        minutes = diff_ms // (60 * 1000)
        diff_ms %= (60 * 1000)

        seconds = diff_ms // 1000
        ms = diff_ms % 1000

        print(f"Difference: {days}d {hours}h {minutes}m {seconds}s {ms}ms")

    # ========================================
    # ORDER MANAGEMENT METHODS
    # ========================================

    @staticmethod
    @api_call(endpoint_type="order_submit", max_retries=2)
    def submit_order(symbol: str, side: str, order_type: str, qty: str,
                     price: str | None = None, time_in_force: str = "GTC",
                     reduce_only: bool = False, order_link_id: str | None = None,
                     connection: 'ExchangeConnection' = None):
        """
        Submit a new order (Market or Limit).

        Args:
            symbol: Trading symbol (e.g., "BTCUSDT")
            side: "Buy" or "Sell"
            order_type: "Market" or "Limit"
            qty: Order quantity as string to preserve precision
            price: Order price (required for Limit orders)
            time_in_force: "GTC", "IOC", or "FOK"
            reduce_only: Whether this is a reduce-only order
            order_link_id: Client order ID for tracking

        Returns:
            dict: Order submission response

        Raises:
            BybitAPIError: If order submission fails
        """
        # Validate required parameters
        if not symbol or not side or not order_type or not qty:
            raise BybitAPIError("Missing required parameters: symbol, side, order_type, qty")

        if order_type == "Limit" and not price:
            raise BybitAPIError("Price is required for Limit orders")

        # Use connection-based API call if connection provided
        if connection:
            import os

            proxy_url = os.getenv("JAPAN_PROXY_URL", "http://***************:8000")
            proxy_token = os.getenv("JAPAN_PROXY_API_TOKEN")

            if not proxy_token:
                raise BybitAPIError("JAPAN_PROXY_API_TOKEN not configured")

            # Decrypt API secret
            from utils.encryption import decrypt_api_secret
            decrypted_secret = decrypt_api_secret(connection.api_secret_encrypted)

            url = f"{proxy_url}/bybit/order/submit"
            headers = {
                "x-api-token": proxy_token,
                "x-bybit-api-key": connection.api_key,
                "x-bybit-api-secret": decrypted_secret,
                "x-bybit-testnet": "true" if connection.is_testnet else "false",
                "Content-Type": "application/json"
            }

            payload = {
                "symbol": symbol.upper(),
                "side": side,
                "orderType": order_type,
                "qty": qty,
                "timeInForce": time_in_force,
                "reduceOnly": reduce_only
            }

            if price:
                payload["price"] = price
            if order_link_id:
                payload["orderLinkId"] = order_link_id

            r = requests.post(url, json=payload, headers=headers, timeout=30)
            Bybit._log_api_request("POST", url, params=payload, headers=headers, response=r)

            return APIErrorHandler.handle_response(r, "order_submit")

        else:
            # Fallback to legacy system
            url = f"{Bybit.get_api_url()}/bybit/order/submit"

            payload = {
                "symbol": symbol.upper(),
                "side": side,
                "orderType": order_type,
                "qty": qty,
                "timeInForce": time_in_force,
                "reduceOnly": reduce_only
            }

            if price:
                payload["price"] = price
            if order_link_id:
                payload["orderLinkId"] = order_link_id

            headers = Bybit.get_headers()
            r = requests.post(url, json=payload, headers=headers, timeout=30)
            Bybit._log_api_request("POST", url, params=payload, headers=headers, response=r)

            # Use enhanced error handling
            return APIErrorHandler.handle_response(r, "order_submit")

    @staticmethod
    def submit_stop_order(symbol: str, side: str, order_type: str, qty: str,
                          trigger_price: str, trigger_direction: int = 1, price: str | None = None,
                          time_in_force: str = "GTC", reduce_only: bool = False,
                          order_link_id: str | None = None, connection: 'ExchangeConnection' = None):
        """
        Submit a stop-loss or take-profit order.

        Args:
            symbol: Trading symbol (e.g., "BTCUSDT")
            side: "Buy" or "Sell"
            order_type: "Stop", "StopLimit", "TakeProfit", or "TakeProfitLimit"
            qty: Order quantity as string
            trigger_price: Stop/TP trigger price
            trigger_direction: 1 for Rising, 2 for Falling
            price: Order price (required for StopLimit/TakeProfitLimit)
            time_in_force: "GTC", "IOC", or "FOK"
            reduce_only: Whether this is a reduce-only order
            order_link_id: Client order ID for tracking
            connection: ExchangeConnection with user credentials

        Returns:
            dict: Order submission response
        """
        # Use connection-based API call if connection provided
        if connection:
            import os

            proxy_url = os.getenv("JAPAN_PROXY_URL", "http://***************:8000")
            proxy_token = os.getenv("JAPAN_PROXY_API_TOKEN")

            if not proxy_token:
                raise BybitAPIError("JAPAN_PROXY_API_TOKEN not configured")

            # Decrypt API secret
            from utils.encryption import decrypt_api_secret
            decrypted_secret = decrypt_api_secret(connection.api_secret_encrypted)

            url = f"{proxy_url}/bybit/order/submit-stop"
            headers = {
                "x-api-token": proxy_token,
                "x-bybit-api-key": connection.api_key,
                "x-bybit-api-secret": decrypted_secret,
                "x-bybit-testnet": "true" if connection.is_testnet else "false",
                "Content-Type": "application/json"
            }

            payload = {
                "symbol": symbol,
                "side": side,
                "orderType": order_type,
                "qty": qty,
                "triggerPrice": trigger_price,
                "triggerDirection": trigger_direction,
                "timeInForce": time_in_force,
                "reduceOnly": reduce_only
            }

            if price:
                payload["price"] = price
            if order_link_id:
                payload["orderLinkId"] = order_link_id

            try:
                r = requests.post(url, json=payload, headers=headers, timeout=30)
                Bybit._log_api_request("POST", url, params=payload, headers=headers, response=r)
                r.raise_for_status()
                return r.json()
            except Exception as e:
                Bybit._log_api_request("POST", url, params=payload, headers=headers, error=e)
                raise

        else:
            # Fallback to legacy system
            url = f"{Bybit.get_api_url()}/bybit/order/submit-stop"

            payload = {
                "symbol": symbol,
                "side": side,
                "orderType": order_type,
                "qty": qty,
                "triggerPrice": trigger_price,
                "triggerDirection": trigger_direction,
                "timeInForce": time_in_force,
                "reduceOnly": reduce_only
            }

            if price:
                payload["price"] = price
            if order_link_id:
                payload["orderLinkId"] = order_link_id

            headers = Bybit.get_headers()
            try:
                r = requests.post(url, json=payload, headers=headers)
                Bybit._log_api_request("POST", url, params=payload, headers=headers, response=r)
                r.raise_for_status()
                return r.json()
            except Exception as e:
                Bybit._log_api_request("POST", url, params=payload, headers=headers, error=e)
                raise

    @staticmethod
    def get_order_status(order_id: str | None = None, order_link_id: str | None = None,
                         connection: 'ExchangeConnection' = None):
        """
        Get order status by order ID or client order ID.

        Args:
            order_id: Bybit order ID
            order_link_id: Client order ID
            connection: ExchangeConnection with user credentials (optional)

        Returns:
            dict: Order status response

        Raises:
            ValueError: If neither order_id nor order_link_id is provided
            BybitAPIError: If API call fails
        """
        if not order_id and not order_link_id:
            raise ValueError("Either order_id or order_link_id must be provided")

        # Use connection-based API call if connection provided
        if connection:
            import os

            proxy_url = os.getenv("JAPAN_PROXY_URL", "http://***************:8000")
            proxy_token = os.getenv("JAPAN_PROXY_API_TOKEN")

            if not proxy_token:
                raise BybitAPIError("JAPAN_PROXY_API_TOKEN not configured")

            # Decrypt API secret
            from utils.encryption import decrypt_api_secret
            decrypted_secret = decrypt_api_secret(connection.api_secret_encrypted)

            url = f"{proxy_url}/bybit/order/status"
            headers = {
                "x-api-token": proxy_token,
                "x-bybit-api-key": connection.api_key,
                "x-bybit-api-secret": decrypted_secret,
                "x-bybit-testnet": "true" if connection.is_testnet else "false",
                "Content-Type": "application/json"
            }

            params = {}
            if order_id is not None:
                params["orderId"] = order_id
            if order_link_id is not None:
                params["orderLinkId"] = order_link_id

            try:
                r = requests.get(url, params=params, headers=headers, timeout=30)
                Bybit._log_api_request("GET", url, params=params, headers=headers, response=r)
                return APIErrorHandler.handle_response(r, "order_status")
            except Exception as e:
                Bybit._log_api_request("GET", url, params=params, headers=headers, error=e)
                raise

        else:
            # Fallback to legacy system
            url = f"{Bybit.get_api_url()}/bybit/order/status"
            params = {}

            if order_id is not None:
                params["orderId"] = order_id
            if order_link_id is not None:
                params["orderLinkId"] = order_link_id

            headers = Bybit.get_headers()
            try:
                r = requests.get(url, params=params, headers=headers)
                Bybit._log_api_request("GET", url, params=params, headers=headers, response=r)
                r.raise_for_status()
                return r.json()
            except Exception as e:
                Bybit._log_api_request("GET", url, params=params, headers=headers, error=e)
                raise

    @staticmethod
    def get_active_orders(symbol: str | None = None, limit: int = 50):
        """
        Get all active orders.

        Args:
            symbol: Trading symbol (optional, gets all symbols if None)
            limit: Maximum number of orders to return

        Returns:
            dict: Active orders response
        """
        url = f"{Bybit.get_api_url()}/bybit/orders/active"
        params: Dict[str, Any] = {"limit": int(limit)}

        if symbol is not None:
            params["symbol"] = str(symbol)

        headers = Bybit.get_headers()
        try:
            r = requests.get(url, params=params, headers=headers)
            Bybit._log_api_request("GET", url, params=params, headers=headers, response=r)
            r.raise_for_status()
            return r.json()
        except Exception as e:
            Bybit._log_api_request("GET", url, params=params, headers=headers, error=e)
            raise

    @staticmethod
    def update_order(symbol: str, order_id: str | None = None, order_link_id: str | None = None,
                     qty: str | None = None, price: str | None = None, trigger_price: str | None = None):
        """
        Update an existing order.

        Args:
            symbol: Trading symbol
            order_id: Bybit order ID
            order_link_id: Client order ID
            qty: New quantity
            price: New price
            trigger_price: New trigger price

        Returns:
            dict: Order update response
        """
        if not order_id and not order_link_id:
            raise ValueError("Either order_id or order_link_id must be provided")

        url = f"{Bybit.get_api_url()}/bybit/order/update"

        payload = {"symbol": symbol}

        if order_id:
            payload["orderId"] = order_id
        if order_link_id:
            payload["orderLinkId"] = order_link_id
        if qty:
            payload["qty"] = qty
        if price:
            payload["price"] = price
        if trigger_price:
            payload["triggerPrice"] = trigger_price

        headers = Bybit.get_headers()
        try:
            r = requests.post(url, json=payload, headers=headers)
            Bybit._log_api_request("POST", url, params=payload, headers=headers, response=r)
            r.raise_for_status()
            return r.json()
        except Exception as e:
            Bybit._log_api_request("POST", url, params=payload, headers=headers, error=e)
            raise

    @staticmethod
    @api_call(endpoint_type="order_cancel", max_retries=2)
    def cancel_order(symbol: str, order_id: str | None = None, order_link_id: str | None = None):
        """
        Cancel a specific order.

        Args:
            symbol: Trading symbol
            order_id: Bybit order ID
            order_link_id: Client order ID

        Returns:
            dict: Order cancellation response

        Raises:
            BybitAPIError: If order cancellation fails
        """
        if not order_id and not order_link_id:
            raise BybitAPIError("Either order_id or order_link_id must be provided")

        if not symbol:
            raise BybitAPIError("Symbol is required for order cancellation")

        url = f"{Bybit.get_api_url()}/bybit/order/cancel"

        payload = {"symbol": symbol.upper()}

        if order_id:
            payload["orderId"] = order_id
        if order_link_id:
            payload["orderLinkId"] = order_link_id

        headers = Bybit.get_headers()
        r = requests.post(url, json=payload, headers=headers, timeout=30)
        Bybit._log_api_request("POST", url, params=payload, headers=headers, response=r)

        return APIErrorHandler.handle_response(r, "order_cancel")

    @staticmethod
    def cancel_all_orders(symbol: str | None = None):
        """
        Cancel all orders for a symbol or all symbols.

        Args:
            symbol: Trading symbol (optional, cancels all symbols if None)

        Returns:
            dict: Cancellation response
        """
        url = f"{Bybit.get_api_url()}/bybit/orders/cancel-all"

        payload = {}
        if symbol:
            payload["symbol"] = symbol

        headers = Bybit.get_headers()
        try:
            r = requests.post(url, json=payload, headers=headers)
            Bybit._log_api_request("POST", url, params=payload, headers=headers, response=r)
            r.raise_for_status()
            return r.json()
        except Exception as e:
            Bybit._log_api_request("POST", url, params=payload, headers=headers, error=e)
            raise

    @staticmethod
    def get_order_history(symbol: str | None = None, limit: int = 50, cursor: str | None = None):
        """
        Get order history with pagination.

        Args:
            symbol: Trading symbol (optional)
            limit: Maximum number of orders to return
            cursor: Pagination cursor

        Returns:
            dict: Order history response
        """
        url = f"{Bybit.get_api_url()}/bybit/orders/history"
        params: Dict[str, Any] = {"limit": int(limit)}

        if symbol is not None:
            params["symbol"] = str(symbol)
        if cursor is not None:
            params["cursor"] = str(cursor)

        headers = Bybit.get_headers()
        try:
            r = requests.get(url, params=params, headers=headers)
            Bybit._log_api_request("GET", url, params=params, headers=headers, response=r)
            r.raise_for_status()
            return r.json()
        except Exception as e:
            Bybit._log_api_request("GET", url, params=params, headers=headers, error=e)
            raise

    # ========================================
    # TRADE MANAGEMENT CONVENIENCE METHODS
    # ========================================

    @staticmethod
    def submit_bracket_order(symbol: str, side: str, qty: str, entry_price: str | None = None,
                             stop_loss_price: str | None = None, take_profit_price: str | None = None,
                             order_link_id: str | None = None):
        """
        Submit a bracket order (entry + stop-loss + take-profit).

        Args:
            symbol: Trading symbol (e.g., "BTCUSDT")
            side: "Buy" or "Sell"
            qty: Order quantity as string
            entry_price: Entry price (None for market entry)
            stop_loss_price: Stop-loss trigger price
            take_profit_price: Take-profit trigger price
            order_link_id: Base client order ID for tracking

        Returns:
            dict: Bracket order response with all order details
        """
        url = f"{Bybit.get_api_url()}/bybit/order/bracket"

        payload = {
            "symbol": symbol,
            "side": side,
            "qty": qty,
            "stopLossPrice": stop_loss_price,
            "takeProfitPrice": take_profit_price
        }

        if entry_price:
            payload["entryPrice"] = entry_price
        if order_link_id:
            payload["orderLinkId"] = order_link_id

        headers = Bybit.get_headers()
        try:
            r = requests.post(url, json=payload, headers=headers)
            Bybit._log_api_request("POST", url, params=payload, headers=headers, response=r)
            r.raise_for_status()
            return r.json()
        except Exception as e:
            Bybit._log_api_request("POST", url, params=payload, headers=headers, error=e)
            raise

    @staticmethod
    def submit_entry_order(symbol: str, side: str, qty: str, price: str | None = None,
                           order_link_id: str | None = None):
        """
        Submit an entry order (market or limit).

        Args:
            symbol: Trading symbol
            side: "Buy" or "Sell"
            qty: Order quantity
            price: Entry price (None for market order)
            order_link_id: Client order ID

        Returns:
            dict: Order submission response
        """
        order_type = "Limit" if price else "Market"
        return Bybit.submit_order(
            symbol=symbol,
            side=side,
            order_type=order_type,
            qty=qty,
            price=price,
            order_link_id=order_link_id
        )

    @staticmethod
    def submit_stop_loss(symbol: str, side: str, qty: str, trigger_price: str,
                         trigger_direction: int = 1, order_link_id: str | None = None,
                         connection: 'ExchangeConnection' = None):
        """
        Submit a stop-loss order.

        Args:
            symbol: Trading symbol
            side: "Buy" or "Sell" (opposite of position side)
            qty: Order quantity
            trigger_price: Stop-loss trigger price
            trigger_direction: 1 for Rising, 2 for Falling
            order_link_id: Client order ID
            connection: ExchangeConnection with user credentials

        Returns:
            dict: Order submission response
        """
        return Bybit.submit_stop_order(
            symbol=symbol,
            side=side,
            order_type="Stop",
            qty=qty,
            trigger_price=trigger_price,
            trigger_direction=trigger_direction,
            reduce_only=True,
            order_link_id=order_link_id,
            connection=connection
        )

    @staticmethod
    def submit_take_profit(symbol: str, side: str, qty: str, trigger_price: str,
                           trigger_direction: int = 1, order_link_id: str | None = None,
                           connection: 'ExchangeConnection' = None):
        """
        Submit a take-profit order.

        Args:
            symbol: Trading symbol
            side: "Buy" or "Sell" (opposite of position side)
            qty: Order quantity
            trigger_price: Take-profit trigger price
            trigger_direction: 1 for Rising, 2 for Falling
            order_link_id: Client order ID
            connection: ExchangeConnection with user credentials

        Returns:
            dict: Order submission response
        """
        return Bybit.submit_stop_order(
            symbol=symbol,
            side=side,
            order_type="TakeProfit",
            qty=qty,
            trigger_price=trigger_price,
            trigger_direction=trigger_direction,
            reduce_only=True,
            order_link_id=order_link_id,
            connection=connection
        )

    @staticmethod
    def get_position_orders(symbol: str):
        """
        Get all orders related to a specific position/symbol.

        Args:
            symbol: Trading symbol

        Returns:
            dict: Orders response filtered by symbol
        """
        return Bybit.get_active_orders(symbol=symbol)

    @staticmethod
    def close_position(symbol: str, qty: str | None = None, price: str | None = None):
        """
        Close a position (market or limit order).

        Args:
            symbol: Trading symbol
            qty: Quantity to close (None for full position)
            price: Close price (None for market close)

        Returns:
            dict: Order submission response
        """
        # First, get current position to determine side and quantity
        try:
            positions_response = Bybit.get_current_open_positions()
            position = None

            for pos in positions_response:
                if pos.symbol == symbol and float(pos.qty) > 0:
                    position = pos
                    break

            if not position:
                raise ValueError(f"No open position found for {symbol}")

            # Determine close side (opposite of position side)
            close_side = "Sell" if position.side.lower() == "buy" else "Buy"
            close_qty = qty or str(position.qty)

            # Submit close order
            order_type = "Limit" if price else "Market"
            return Bybit.submit_order(
                symbol=symbol,
                side=close_side,
                order_type=order_type,
                qty=close_qty,
                price=price,
                reduce_only=True,
                order_link_id=f"close_{symbol}_{int(__import__('time').time())}"
            )

        except Exception as e:
            logger.error(f"Error closing position for {symbol}: {e}")
            raise

    # ========================================
    # REAL-TIME ORDER MONITORING
    # ========================================

    @staticmethod
    def poll_order_updates(order_ids: list, interval_seconds: int = 5, max_polls: int = 60):
        """
        Poll for order status updates (fallback for WebSocket).

        Args:
            order_ids: List of order IDs to monitor
            interval_seconds: Polling interval in seconds
            max_polls: Maximum number of polls before giving up

        Returns:
            dict: Final status of all orders
        """
        import time

        results = {}
        polls_count = 0

        logger.info(f"🔄 Starting order polling for {len(order_ids)} orders")

        while polls_count < max_polls and order_ids:
            try:
                # Get active orders
                active_response = Bybit.get_active_orders()
                active_orders = active_response.get("result", {}).get("list", [])

                # Check each order
                remaining_orders = []
                for order_id in order_ids:
                    # Look for order in active orders
                    found = False
                    for order in active_orders:
                        if order.get("orderId") == order_id:
                            status = order.get("orderStatus")
                            results[order_id] = {
                                "status": status,
                                "filled_qty": order.get("cumExecQty", "0"),
                                "remaining_qty": order.get("leavesQty", "0"),
                                "avg_price": order.get("avgPrice", "0"),
                                "last_update": time.time()
                            }

                            # Keep monitoring if still active
                            if status in ["New", "PartiallyFilled", "Untriggered"]:
                                remaining_orders.append(order_id)

                            found = True
                            break

                    # If not found in active orders, check order history
                    if not found:
                        try:
                            history_response = Bybit.get_order_history(limit=50)
                            history_orders = history_response.get("result", {}).get("list", [])

                            for order in history_orders:
                                if order.get("orderId") == order_id:
                                    results[order_id] = {
                                        "status": order.get("orderStatus"),
                                        "filled_qty": order.get("cumExecQty", "0"),
                                        "remaining_qty": order.get("leavesQty", "0"),
                                        "avg_price": order.get("avgPrice", "0"),
                                        "last_update": time.time()
                                    }
                                    found = True
                                    break
                        except Exception as e:
                            logger.warning(f"⚠️ Error checking order history for {order_id}: {e}")

                    if not found:
                        remaining_orders.append(order_id)

                order_ids = remaining_orders
                polls_count += 1

                if order_ids:
                    logger.info(f"🔄 Poll {polls_count}: {len(order_ids)} orders still being monitored")
                    time.sleep(interval_seconds)
                else:
                    logger.info(f"✅ All orders completed after {polls_count} polls")
                    break

            except Exception as e:
                logger.error(f"❌ Error during order polling: {e}")
                print(f"❌ Error during order polling: {str(e)}", file=sys.stderr)
                traceback.print_exc()
                polls_count += 1
                time.sleep(interval_seconds)

        if order_ids:
            logger.warning(f"⚠️ Polling timeout: {len(order_ids)} orders still pending")
            for order_id in order_ids:
                if order_id not in results:
                    results[order_id] = {
                        "status": "Unknown",
                        "error": "Polling timeout",
                        "last_update": time.time()
                    }

        return results

    @staticmethod
    def monitor_trade_execution(trade_id: str, timeout_minutes: int = 30):
        """
        Monitor execution of all orders in a trade.

        Args:
            trade_id: Trade exchange_trade_id
            timeout_minutes: Maximum time to monitor

        Returns:
            dict: Execution monitoring results
        """
        # This would integrate with the order execution service
        # to monitor the progress of trade execution
        logger.info(f"📊 Monitoring trade execution: {trade_id}")

        return {
            "trade_id": trade_id,
            "status": "monitoring_not_implemented",
            "message": "Trade execution monitoring not yet implemented"
        }

    @staticmethod
    def start_monitoring_session(connection) -> Optional[str]:
        """
        Start a WebSocket monitoring session for real-time updates.
        
        Args:
            connection: ExchangeConnection containing api_key and encrypted secret
            
        Returns:
            str: Session ID on success, None on failure
        """
        try:
            base_url = Bybit.get_api_url()
            base_headers = Bybit.get_headers()
            proxy_token = os.getenv("JAPAN_PROXY_API_TOKEN")

            if not proxy_token:
                logger.warning("⚠️ JAPAN_PROXY_API_TOKEN not configured")
                return None

            # Decrypt API secret
            try:
                from utils.encryption import decrypt_api_secret
                api_secret = decrypt_api_secret(getattr(connection, 'api_secret_encrypted', ''))
            except Exception as e:
                logger.error(f"❌ Failed to decrypt Bybit API secret: {e}")
                print(f"❌ Failed to decrypt Bybit API secret: {str(e)}", file=sys.stderr)
                traceback.print_exc()
                return None

            api_key = getattr(connection, 'api_key', None)
            if not api_key or not api_secret:
                logger.error("❌ Missing API credentials on ExchangeConnection")
                return None

            # Get testnet flag from the connection
            testnet = bool(getattr(connection, 'is_testnet', False))

            # Start monitoring session
            start_url = f"{base_url}/bybit/monitor/start"
            payload = {
                "api_key": api_key,
                "api_secret": api_secret,
                "testnet": testnet,
                "order_ids": []
            }

            r_start = requests.post(start_url, json=payload, headers=base_headers, timeout=15)
            Bybit._log_api_request("POST", start_url, params=payload, headers=base_headers, response=r_start)
            r_start.raise_for_status()

            session_id = (r_start.json() or {}).get("session_id")
            if session_id:
                logger.info(f"📡 Started monitoring session: {session_id}")
            return session_id

        except requests.exceptions.HTTPError as e:
            if getattr(e, 'response', None) and e.response.status_code == 404:
                logger.info("📡 /bybit/monitor/start not available (404). Proceeding without session.")
            else:
                Bybit._log_api_request("POST", f"{base_url}/bybit/monitor/start", params={}, headers=base_headers,
                                       error=e)
            return None
        except Exception as e:
            logger.warning(f"⚠️ Could not start monitoring session: {e}")
            return None

    @staticmethod
    def stop_monitoring_session(session_id: str):
        """
        Stop a WebSocket monitoring session.
        
        Args:
            session_id: Session ID to stop
        """
        try:
            base_url = Bybit.get_api_url()
            base_headers = Bybit.get_headers()

            stop_url = f"{base_url}/bybit/monitor/stop"
            payload = {"session_id": session_id}

            r_stop = requests.post(stop_url, json=payload, headers=base_headers, timeout=10)
            Bybit._log_api_request("POST", stop_url, params=payload, headers=base_headers, response=r_stop)

            if r_stop.status_code == 200:
                logger.info(f"🛑 Stopped monitoring session: {session_id}")
            else:
                logger.warning(f"⚠️ Failed to stop monitoring session: {r_stop.status_code}")

        except Exception as e:
            logger.warning(f"⚠️ Could not stop monitoring session: {e}")

    @staticmethod
    def get_instruments_info(category: str, symbol: str = None, status: str = None,
                             base_coin: str = None, limit: int = None, cursor: str = None):
        """
        Get instruments info from Bybit.

        Args:
            category: Product type ("spot", "linear", "inverse", "option")
            symbol: Trading pair symbol (optional)
            status: Instrument status filter (optional)
            base_coin: Base coin filter (optional)
            limit: Number of results, max 1000 (optional)
            cursor: Pagination cursor (optional)

        Returns:
            dict: Instruments info response from Bybit
        """
        url = f"{Bybit.get_api_url()}/bybit/instruments-info"
        params = {"category": category}

        if symbol:
            params["symbol"] = symbol
        if status:
            params["status"] = status
        if base_coin:
            params["baseCoin"] = base_coin
        if limit:
            params["limit"] = min(max(1, limit), 1000)
        if cursor:
            params["cursor"] = cursor

        headers = Bybit.get_headers()
        try:
            r = requests.get(url, params=params, headers=headers, timeout=10)
            Bybit._log_api_request("GET", url, params=params, headers=headers, response=r)
            r.raise_for_status()
            return r.json()
        except Exception as e:
            Bybit._log_api_request("GET", url, params=params, headers=headers, error=e)
            raise
