"""
Trade Creation Service

Handles the creation of structured Trade and Order objects from various drawing tools
(Fibonacci retracements, rectangles, lines, etc.) for future API execution.
"""

import uuid
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, Any

from models.trade import TradeSource, TradeNames
from trades_db import TradesDB


class OrderType:
    """Order type constants for trade setups"""
    STOP_LOSS = "Stop Loss"
    ENTRY = "Entry"
    TAKE_PROFIT = "Take Profit"
    MARKET = "Market"
    LIMIT = "Limit"
    STOP_LIMIT = "Stop Limit"


class OrderStatus:
    """Order status constants for trade setups"""
    OPEN = "Open"
    SETUP = "Setup"
    PENDING = "Pending"
    FILLED = "Filled"
    CANCELLED = "Cancelled"


class TradeCreationService:
    """Service for creating structured trades from drawing tool data"""

    @staticmethod
    def create_fibonacci_trade(fib_data: Dict[str, Any], account_data: Dict[str, Any], username: str):
        """
        Create a Trade object with Orders from Fibonacci retracement data.

        Args:
            fib_data: Dictionary containing Fibonacci level data
            account_data: Dictionary containing balance and risk percentage
            username: Username associated with the trade

        Returns:
            Trade object with structured orders
        """
        # Import here to avoid circular imports
        from models.trade import TradeStatus, TradeDirection, Exchange
        from models.order import BuySell
        
        # Extract account data
        account_balance = Decimal(str(account_data.get("balance", 0.0)))
        risk_percentage = Decimal(account_data.get("risk", 0.0))
        exchange_str = account_data.get("exchange", "UNKNOWN").upper()
        
        # Extract data from request
        symbol = fib_data.get("symbol", "UNKNOWN")
        timeframe = fib_data.get("timeframe", "1h")

        # Apply adaptive precision to all price values
        from models.trade import Trade
        p1 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("p1", 0))))
        p2 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("p2", 0))))
        fib50 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("fib50", 0))))
        fib618 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("fib618", 0))))
        fib786 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("fib786", 0))))
        fibtp236 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("fibtp236", 0))))
        fibtp382 = Trade.apply_adaptive_precision(Decimal(str(fib_data.get("fibtp382", 0))))
        timestamp = fib_data.get("timestamp")

        # Determine trade direction
        direction = TradeDirection.LONG if p1 < p2 else TradeDirection.SHORT

        # Calculate risk-based position sizing
        risk_amount = account_balance * (Decimal(str(risk_percentage)) / Decimal("100"))
        
        # Calculate average entry price for risk calculation
        entry_prices = [fib50, fib618, fib786]
        avg_entry_price = sum(entry_prices) / len(entry_prices)
        
        # Calculate risk per unit (distance from avg entry to stop loss)
        risk_per_unit = abs(avg_entry_price - p1)
        
        # Calculate total position size based on risk
        if risk_per_unit > 0:
            total_position_size = risk_amount / risk_per_unit
        else:
            total_position_size = Decimal("0.001")  # Fallback minimum
        
        # Determine minimum order size and step size dynamically from Bybit instruments info
        min_size, qty_step = TradeCreationService._get_min_order_size(symbol, exchange_str)
        
        # Ensure total position meets minimum requirements and is properly stepped
        total_position_size = max(total_position_size, min_size * 3)  # At least 3x min for 3 entries
        total_position_size = TradeCreationService._round_to_step(total_position_size, qty_step)
        
        # Split position across entry levels (equal weighting)
        entry_size = total_position_size / Decimal("3")
        entry_size = max(entry_size, min_size)  # Ensure each entry meets minimum
        entry_size = TradeCreationService._round_to_step(entry_size, qty_step)
        
        # Take profit sizes (split total position)
        tp_size = total_position_size / Decimal("2")  # Half position each
        tp_size = max(tp_size, min_size)  # Ensure each TP meets minimum
        tp_size = TradeCreationService._round_to_step(tp_size, qty_step)

        # Create timestamp
        trade_time = datetime.now(timezone.utc)
        if timestamp:
            trade_time = datetime.fromtimestamp(timestamp / 1000, timezone.utc)

        # Generate unique trade ID
        trade_id = str(uuid.uuid4())

        # Create an order list
        orders = []

        # 1. Stop Loss Order (FIB P1)
        stop_loss_order = TradeCreationService._create_order(
            order_id=f"{trade_id}",
            symbol=symbol,
            order_type=OrderType.STOP_LOSS,
            price=p1,
            quantity=total_position_size,  # Full position size
            buy_sell=BuySell.SELL if direction == TradeDirection.LONG else BuySell.BUY,
            created_date=trade_time
        )
        orders.append(stop_loss_order)

        # 2. Entry Orders (50%, 61.8%, 78.6% retracements)
        entry_levels = [
            {"level": "50%", "price": fib50, "qty": entry_size},
            {"level": "61.8%", "price": fib618, "qty": entry_size},
            {"level": "78.6%", "price": fib786, "qty": entry_size}
        ]

        for i, entry in enumerate(entry_levels):
            entry_order = TradeCreationService._create_order(
                order_id=f"{trade_id}_ENTRY_{i + 1}",
                symbol=symbol,
                order_type=OrderType.ENTRY,
                price=entry["price"],
                quantity=entry["qty"],
                buy_sell=BuySell.BUY if direction == TradeDirection.LONG else BuySell.SELL,
                created_date=trade_time
            )
            orders.append(entry_order)

        # 3. Take Profit Orders (23.6% and 38.2% extensions)
        tp_levels = [
            {"level": "23.6%", "price": fibtp236, "qty": tp_size, "note": "Half position"},
            {"level": "38.2%", "price": fibtp382, "qty": tp_size, "note": "Remaining position"}
        ]

        for i, tp in enumerate(tp_levels):
            tp_order = TradeCreationService._create_order(
                order_id=f"{trade_id}_TP_{i + 1}",
                symbol=symbol,
                order_type=OrderType.TAKE_PROFIT,
                price=tp["price"],
                quantity=tp["qty"],
                buy_sell=BuySell.SELL if direction == TradeDirection.LONG else BuySell.BUY,
                created_date=trade_time
            )
            orders.append(tp_order)

        # Calculate trade metrics with adaptive precision
        # total_quantity = sum(order.quantity for order in orders if order.orderType == OrderType.ENTRY)
        # avg_entry_price = Trade.apply_adaptive_precision(
        #     sum(order.price * order.quantity for order in orders if order.orderType == OrderType.ENTRY) / total_quantity
        # )
        # risk_amount = Trade.apply_adaptive_precision(abs(avg_entry_price - p1) * total_quantity)
        # risk_percent = Trade.apply_adaptive_precision((risk_amount / account_size) * Decimal("100"))
        # notional = Trade.apply_adaptive_precision(avg_entry_price * total_quantity)

        strategy_id = TradesDB.get_or_create_strategy_by_name(TradeNames.SYSTEM_FIBONACCI.value)

        # Create Trade object
        trade = Trade(
            id_field=None,  # Will be set when saved to database
            exchange_trade_id=trade_id,
            trade_orders=orders,
            unfilled_orders=[],
            symbol=symbol,
            accountBalance=account_balance,
            exchange_connection_id=None,
            direction=direction,
            timeOpen=trade_time,
            status=TradeStatus.SETUP,
            tradeQty=None,
            openQty=None,  # No position opened yet
            lastUpdate=trade_time,
            timeClose=None,
            duration=0,
            chartLink=None,
            notes=None,
            notional=None,
            leverage=None,
            avgOpenPrice=None,
            avgClosePrice=None,
            riskAmt=None,
            riskPercent=None,
            profit=None,
            fees=None,
            strategy=strategy_id,
            time_frame=timeframe,
            source=TradeSource.SYSTEM_FIBONACCI,
            username=username
        )
        trade.update_trade_details()
        return trade

    @staticmethod
    def _create_order(order_id: str, symbol: str, order_type: str, price: Decimal,
                      quantity: Decimal, buy_sell, created_date: datetime):
        """Create a standardized Order object for trade setups"""
        from models.trade import Trade
        from models.order import Order

        return Order(
            id_field=None,  # Will be set when saved to database
            order_id=order_id,
            trade_id=None,  # Will be set when associated with trade
            created_date=created_date,
            filled_date=None,
            symbol=symbol,
            orderType=order_type,
            orderStatus=OrderStatus.SETUP,
            buySell=buy_sell,
            reduce=order_type == OrderType.STOP_LOSS or order_type == OrderType.TAKE_PROFIT,
            price=Trade.apply_adaptive_precision(price),  # Apply adaptive precision to price
            fillPrice=Decimal("0"),
            fee=Decimal("0"),
            quantity=quantity,
            filledQuantity=Decimal("0"),
            sierraActivity=None,
            coinbaseOrder=None,
            coinbaseFill=None,
            bybitOrder=None
        )

    @staticmethod
    def create_rectangle_trade(rect_data: Dict[str, Any], account_size: Decimal = Decimal("10000")):
        """
        Create a Trade object from rectangle drawing data.
        Future implementation for rectangle-based trades.
        """
        # Placeholder for future rectangle trade implementation
        raise NotImplementedError("Rectangle trade creation not yet implemented")

    @staticmethod
    def create_line_trade(line_data: Dict[str, Any], account_size: Decimal = Decimal("10000")):
        """
        Create a Trade object from line drawing data.
        Future implementation for line-based trades.
        """
        # Placeholder for future line trade implementation
        raise NotImplementedError("Line trade creation not yet implemented")

    @staticmethod
    def _get_min_order_size(symbol: str, exchange_str: str) -> tuple[Decimal, Decimal]:
        """
        Get minimum order size and quantity step for a symbol from exchange instruments info.
        
        Args:
            symbol: Trading symbol (e.g., "BTCUSDT")
            exchange_str: Exchange name (e.g., "BYBIT")
            
        Returns:
            tuple: (min_order_qty, qty_step) as Decimals
        """
        # Default fallback values
        default_min_sizes = {
            "DEFAULT": (Decimal("0.001"), Decimal("0.001"))
        }
        
        # Only fetch from Bybit for now
        if exchange_str.upper() != "BYBIT":
            # Use hardcoded fallback for non-Bybit exchanges
            for coin in default_min_sizes:
                if coin in symbol.upper() and coin != "DEFAULT":
                    return default_min_sizes[coin]
            return default_min_sizes["DEFAULT"]
        
        try:
            from services.bybit import Bybit
            
            # Fetch instrument info from Bybit
            response = Bybit.get_instruments_info("linear", symbol=symbol)
            
            # Extract minimum order quantity and step size from response
            result_list = response.get("result", {}).get("list", [])
            if result_list:
                instrument = result_list[0]  # First matching instrument
                lot_size_filter = instrument.get("lotSizeFilter", {})
                min_order_qty = lot_size_filter.get("minOrderQty")
                qty_step = lot_size_filter.get("qtyStep")
                
                if min_order_qty and qty_step:
                    min_size = Decimal(str(min_order_qty))
                    step_size = Decimal(str(qty_step))
                    print(f"[TRADE_CREATION] Retrieved for {symbol}: min={min_size}, step={step_size}")
                    return min_size, step_size
            
            print(f"[TRADE_CREATION] No instrument info found for {symbol}, using fallback")
            
        except Exception as e:
            print(f"[TRADE_CREATION] Error fetching instrument info for {symbol}: {e}, using fallback")
        
        # Fallback to hardcoded logic if API call fails
        for coin in default_min_sizes:
            if coin in symbol.upper() and coin != "DEFAULT":
                return default_min_sizes[coin]
        return default_min_sizes["DEFAULT"]

    @staticmethod
    def _round_to_step(quantity: Decimal, step_size: Decimal) -> Decimal:
        """
        Round quantity to the nearest valid step size.
        
        Args:
            quantity: The quantity to round
            step_size: The step size from exchange (e.g., 0.001)
            
        Returns:
            Decimal: Quantity rounded to nearest valid step
        """
        if step_size <= 0:
            return quantity
            
        # Round to nearest step (round up to ensure we meet minimums)
        steps = (quantity / step_size).quantize(Decimal('1'), rounding='ROUND_UP')
        return steps * step_size
