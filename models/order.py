import json
import logging
import sys
import traceback
from decimal import Decimal
from enum import Enum

from coinbase.rest.types.orders_types import OrderConfiguration

import helper
from models.bybit_order import BybitOrder
from models.bybit_position import BybitPosition
from models.coinbase_fill import CoinbaseFill
from models.coinbase_order import CoinbaseOrder
from models.sierra_activity import SierraActivity


class BuySell(Enum):
    BUY = "Buy"
    SELL = "Sell"


PRICE_KEYS = [
    "market_market_ioc",
    "limit_limit_gtc",
    "limit_limit_gtd",
    "sor_limit_ioc",
    "stop_limit_stop_limit_gtc",
    "stop_limit_stop_limit_gtd",
    "limit_limit_fok"
]


def get_coinbase_order_price(cbo):
    """ Extracts order price from Coinbase order configuration """
    order_config = cbo.order_configuration

    if isinstance(order_config, OrderConfiguration):
        for key in PRICE_KEYS:
            if hasattr(order_config, key):
                return cbo.average_filled_price if key == "market_market_ioc" else Decimal(
                    getattr(order_config, key).limit_price)
    else:
        for key in PRICE_KEYS:
            if key in order_config:
                return cbo.average_filled_price if key == "market_market_ioc" else Decimal(
                    _get_order_config_value(cbo, key, "limit_price"))

    return None


def get_coinbase_order_quantity(cbo):
    """ Extracts order quantity from Coinbase order configuration """
    order_config = cbo.order_configuration

    if cbo.status.lower() in {"cancelled", "open"}:
        if isinstance(order_config, OrderConfiguration):
            for key in PRICE_KEYS:
                if hasattr(order_config, key):
                    return cbo.filled_size if key == "market_market_ioc" else Decimal(
                        getattr(order_config, key).base_size)
        else:
            for key in PRICE_KEYS:
                if key in order_config:
                    return cbo.filled_size if key == "market_market_ioc" else Decimal(
                        _get_order_config_value(cbo, key, "base_size"))

    return cbo.filled_size


def _get_order_config_value(cbo, order_type, key):
    """ Fetches order configuration value with safe access """
    return cbo.order_configuration.get(order_type, {}).get(key, None)


class Order:
    def __init__(self, id_field, order_id, trade_id, created_date, filled_date, symbol, orderType, orderStatus, buySell,
                 reduce, price, fillPrice, fee, quantity, filledQuantity, sierraActivity, coinbaseOrder, coinbaseFill,
                 bybitOrder):
        self.id_field = id_field
        self.order_id = order_id
        self.trade_id = trade_id
        self.created_date = created_date
        self.filled_date = filled_date
        self.symbol = symbol
        self.orderType = orderType
        self.orderStatus = orderStatus
        self.buySell = buySell
        self.reduce = reduce
        self.price = price
        self.fillPrice = fillPrice
        self.quantity = quantity
        self.filledQuantity = filledQuantity
        self.filled = self.fillPrice > 0 and self.filledQuantity > 0
        self.fee = fee
        self.sierraActivity = sierraActivity
        self.coinbaseOrder = coinbaseOrder
        self.coinbaseFill = coinbaseFill
        self.bybitOrder = bybitOrder

    @classmethod
    def fromCoinbaseFill(cls, fill: CoinbaseFill):
        return cls(
            id_field=None,
            order_id=fill.coinbase_order_id,
            trade_id=None,
            created_date=fill.sequence_timestamp,  # TODO Fix the dates
            filled_date=fill.trade_time,  # TODO Fix the dates
            symbol=fill.product_id,
            orderType=fill.liquidity_indicator,
            orderStatus=fill.trade_type,
            buySell=BuySell.BUY if fill.side.lower() == "buy" else BuySell.SELL,
            reduce=False,
            price=fill.price,
            fillPrice=fill.price,
            fee=fill.commission,
            quantity=fill.size,
            filledQuantity=fill.size,
            sierraActivity=None,
            coinbaseFill=fill,
            coinbaseOrder=None,
            bybitOrder=None
        )

    @classmethod
    def fromCoinbaseOrder(cls, cbo: CoinbaseOrder):
        return cls(
            id_field=None,
            order_id=cbo.coinbase_order_id,
            trade_id=None,
            created_date=cbo.created_time,
            filled_date=cbo.last_fill_time,
            symbol=cbo.product_id,
            orderType=cbo.order_type,
            orderStatus=cbo.status,
            buySell=BuySell.BUY if cbo.side.lower() == "buy" else BuySell.SELL,
            reduce=False,
            price=get_coinbase_order_price(cbo),
            fillPrice=cbo.average_filled_price,
            fee=cbo.total_fees,
            quantity=get_coinbase_order_quantity(cbo),
            filledQuantity=cbo.filled_size,
            sierraActivity=None,
            coinbaseFill=None,
            coinbaseOrder=cbo,
            bybitOrder=None
        )

    @classmethod
    def fromActivity(cls, act: SierraActivity):
        return cls(
            id_field=None,
            order_id=act.internalOrderId,  # TODO Check this ID is unique
            trade_id=None,
            created_date=act.date,  # TODO Fix the dates
            filled_date=act.transDate,  # TODO Fix the dates
            symbol=act.symbol,
            orderType=act.orderType,
            orderStatus=act.orderStatus,
            buySell=BuySell.BUY if act.buySell.lower() == "buy" else BuySell.SELL,
            reduce=act.openClose.lower() == "close",
            price=act.price,
            fillPrice=act.fillPrice,
            fee=Decimal(0),
            quantity=act.quantity,
            filledQuantity=act.filledQuantity,
            sierraActivity=act,
            coinbaseFill=None,
            coinbaseOrder=None,
            bybitOrder=None
        )

    @classmethod
    def from_bybit_order(cls, od: BybitOrder):
        return cls(
            id_field=None,
            order_id=od.bybit_order_id,
            trade_id=None,
            created_date=od.createdTime,  # TODO Fix the dates
            filled_date=od.updatedTime,  # TODO Fix the dates
            symbol=od.symbol,
            orderType=od.orderType,
            orderStatus=od.orderStatus,
            buySell=BuySell.BUY if od.side.lower() == "buy" else BuySell.SELL,
            reduce=od.reduceOnly,
            price=od.price,
            fillPrice=od.avgPrice,
            fee=Decimal(0),
            quantity=od.qty,
            filledQuantity=od.cumExecQty,
            sierraActivity=None,
            coinbaseFill=None,
            coinbaseOrder=None,
            bybitOrder=od
        )

    @classmethod
    def from_bybit_position(cls, position: BybitPosition):
        import logging
        from datetime import datetime
        import pytz

        # Use current time if datetime is not available
        current_time = datetime.now(pytz.timezone("America/New_York"))

        try:
            # Use the datetime objects directly from the position if available
            created_date = position.created_datetime if position.created_datetime else current_time
            filled_date = position.updated_datetime if position.updated_datetime else current_time

            # Determine the entry price to use
            entry_price = position.avg_entry_price
            if entry_price == 0 and position.mark_price > 0:
                # If avg_entry_price is not available, use mark_price as a fallback
                entry_price = position.mark_price
                logging.warning(f"Using mark_price ({entry_price}) as entry price for {position.symbol}")

            # Create the Order object
            return cls(
                id_field=None,
                order_id=position.order_id,
                trade_id=None,
                created_date=created_date,
                filled_date=filled_date,
                symbol=position.symbol,
                orderType="Position",  # Indicate this is a position, not a regular order
                orderStatus="Filled",  # Positions are considered filled
                buySell=BuySell.BUY if position.side.lower() == "buy" else BuySell.SELL,
                reduce=False,  # Positions are not reduce-only
                price=entry_price,  # Use the determined entry price
                fillPrice=entry_price,
                fee=Decimal(0),  # Fees are not typically available in position data
                quantity=position.qty,  # Already a Decimal from BybitPosition
                filledQuantity=position.qty,  # Already a Decimal from BybitPosition
                sierraActivity=None,
                coinbaseFill=None,
                coinbaseOrder=None,
                bybitOrder=None
            )
        except Exception as e:
            logging.error(f"Error creating Order from BybitPosition: {e}")
            logging.error(f"Position data: {position.raw}")
            print(f"❌ Error creating Order from BybitPosition: {str(e)}", file=sys.stderr)
            print(f"❌ Position data: {position.raw}", file=sys.stderr)
            traceback.print_exc()
            raise

    @classmethod
    def fromRow(cls, row):
        return cls(
            id_field=row["o_id"],
            order_id=row["order_id"],
            trade_id=row["trade_id"],
            created_date=helper.mSToDate(row["created_date"]),
            filled_date=helper.mSToDate(row["filled_date"]),
            symbol=row["o_symbol"],  # Use o_symbol to avoid conflict with trade symbol
            orderType=row["orderType"],
            orderStatus=row["orderStatus"],
            buySell=BuySell(row["buySell"]),
            reduce=row["reduce"],
            price=helper.check_decimal(row["price"]),
            fillPrice=helper.check_decimal(row["fillPrice"]),
            fee=helper.check_decimal(row["fee"]),
            quantity=helper.check_decimal(row["quantity"]),
            filledQuantity=helper.check_decimal(row["filledQuantity"]),
            sierraActivity=None,
            coinbaseFill=None,
            coinbaseOrder=None,
            bybitOrder=None,
        )

    def printInfo(self):
        print('\n' + '-' * 20)
        print(f"Created Date: {helper.formatDate(self.created_date)}")
        print(f"Filled:      {self.filled}")
        print(f"OrderStatus: {self.orderStatus}")
        print(f"Filled Date: {helper.formatDate(self.filled_date)}")
        print(f"Symbol:      {self.symbol}")
        print(f"BuySell:     {self.buySell.value}")
        print(f"Reduce:      {self.reduce}")
        print(f"Quantity:    {self.filledQuantity}")
        print(f"Price:       {self.fillPrice}")

    def to_dict(self):
        return {
            "order_id": self.order_id,
            "created_date": helper.date_to_ms(self.created_date),
            "filled_date": helper.date_to_ms(self.filled_date),
            "symbol": self.symbol,
            "orderType": self.orderType,
            "orderStatus": self.orderStatus,
            "buySell": self.buySell.value,  # Ensure proper JSON serialization
            "reduce": self.reduce,
            "price": str(self.price),
            "fillPrice": str(self.fillPrice),
            "quantity": str(self.quantity),
            "filledQuantity": str(self.filledQuantity),
            "filled": self.filled,
            "fee": str(self.fee),
            "trade_id": self.trade_id
        }

    def to_json(self):
        return json.dumps(self.to_dict(), indent=4)

    def __repr__(self):
        """
        Returns a comprehensive string representation of the Order object.
        
        Returns:
            str: A formatted string with key order details
        """
        # Determine fill status
        fill_status = "FILLED" if self.filled else "UNFILLED"

        # Format prices with 2 decimal places
        price_str = f"${self.price:.2f}" if self.price is not None else "N/A"
        fill_price_str = f"${self.fillPrice:.2f}" if self.fillPrice is not None else "N/A"

        # Build the representation string
        return (
            f"{self.symbol} {self.buySell.value} {self.quantity}/{self.filledQuantity} "
            f"({fill_status}) "  # @ {price_str}/{fill_price_str}
            f"Type: {self.orderType} {"Reduce" if self.reduce else ""} | Status: {self.orderStatus}"
        )
