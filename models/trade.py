import json
import sys
import traceback
from datetime import datetime, timezone
from decimal import Decimal, ROUND_HALF_UP
from enum import Enum

import helper
import services.coinbase_advanced
import trades_db
from models.order import BuySell


class TradeNames(Enum):
    SYSTEM_FIBONACCI = "System Fibonacci"


class TradeSource(Enum):
    MANUAL_ENTRY = "manual_entry"
    SYSTEM = "system"
    API = "api"
    SYSTEM_FIBONACCI = "system_fibonacci"
    AUTO_TP = "auto_tp"
    AUTO_SL = "auto_sl"
    IMPORT = "import"


def get_coinbase_futures_balance():
    summary = services.coinbase_advanced.get_futures_balance_summary()
    balance = Decimal(summary["balance_summary"]["total_usd_balance"]["value"])
    return balance


def get_exchange(order):
    """
    Determines the exchange for an order based on its properties.

    Args:
        order: The Order object

    Returns:
        Exchange: The exchange enum value
    """
    if order.coinbaseOrder is not None:
        return Exchange.COINBASE
    elif order.coinbaseFill is not None:
        return Exchange.COINBASE
    elif order.sierraActivity is not None:
        return Exchange.EDGECME
    elif order.bybitOrder is not None:
        return Exchange.BYBIT
    # For orders created from Bybit positions (they don't have a bybitOrder property set)
    elif order.orderType == "Position" and "USDT" in order.symbol:
        return Exchange.BYBIT
    return Exchange.UNKNOWN


class Trade:
    def __init__(self, id_field, exchange_trade_id, trade_orders, unfilled_orders, symbol, accountBalance, exchange_connection_id,
                 direction, timeOpen, status, tradeQty, openQty, lastUpdate, timeClose, duration, chartLink, notes,
                 notional, leverage, avgOpenPrice, avgClosePrice, riskAmt, riskPercent, profit, fees, strategy,
                 time_frame, source, username):
        self.id_field = id_field
        self.exchange_trade_id = exchange_trade_id
        self.trade_orders = trade_orders
        self.unfilled_orders = unfilled_orders
        self.symbol = symbol
        self.accountBalance = accountBalance
        self.exchange_connection_id = exchange_connection_id
        self.direction = direction
        self.timeOpen = timeOpen
        self.status = status
        self.tradeQty = tradeQty
        self.openQty = openQty
        self.lastUpdate = lastUpdate
        self.timeClose = timeClose
        self.duration = duration
        self.chartLink = chartLink
        self.notes = notes
        self.notional = notional
        self.leverage = leverage
        self.avgOpenPrice = avgOpenPrice
        self.avgClosePrice = avgClosePrice
        self.riskAmt = riskAmt
        self.riskPercent = riskPercent
        self.profit = profit
        self.fees = fees
        self.strategy = strategy
        self.time_frame = time_frame
        self.source = source
        self.username = username

    @classmethod
    def fromOrder(cls, order, username, building_reversed=False, direction=None, source=TradeSource.IMPORT):
        # Determine the exchange
        exchange = get_exchange(order)

        # Get the appropriate account balance based on the exchange
        if exchange == Exchange.BYBIT:
            from services.bybit import Bybit
            account_balance = "0"  # Bybit.getAccountBalanceDecimal()
        elif exchange == Exchange.COINBASE:
            account_balance = "0"  # get_coinbase_futures_balance()
        else:
            account_balance = Decimal(0)

        return cls(
            id_field=None,
            exchange_trade_id=order.order_id,
            trade_orders=[order],
            unfilled_orders=[],
            symbol=order.symbol,
            timeOpen=order.filled_date if order.filled_date is not None else order.created_date,
            lastUpdate=None,  # Will be set after instantiation
            timeClose=None,
            exchange_connection_id=exchange_connection_id,
            direction=direction if direction is not None else cls.trade_direction(building_reversed, order),
            accountBalance=account_balance,
            status=TradeStatus.OPEN,
            tradeQty=None,  # Will be set after instantiation
            openQty=None,  # Will be set after instantiation
            duration=None,
            chartLink=None,
            notes=None,
            notional=None,  # Will be set after instantiation
            leverage=None,
            avgOpenPrice=None,  # Will be set after instantiation
            avgClosePrice=None,  # Will be set after instantiation
            riskAmt=None,  # Will be set after instantiation
            riskPercent=None,  # Will be set after instantiation
            profit=Decimal(0),
            fees=None,  # Will be set after instantiation
            strategy=None,
            time_frame=None,
            source=source,
            username=username
        )

    @classmethod
    def trade_direction(cls, building_reversed, order):
        if building_reversed:
            return TradeDirection.SHORT if order.buySell == BuySell.BUY else TradeDirection.LONG
        else:
            return TradeDirection.LONG if order.buySell == BuySell.BUY else TradeDirection.SHORT

    @classmethod
    def fromDBRow(cls, row, order):
        """ Creates a Trade instance from a database row. """
        return cls(
            id_field=row["t_id"],  # Use t_id instead of id
            exchange_trade_id=row["exchange_trade_id"],
            trade_orders=[order],
            unfilled_orders=[],
            status=TradeStatus[row['status']],
            tradeQty=helper.check_decimal(row["tradeQty"]),
            openQty=helper.check_decimal(row["openQty"]),
            timeOpen=helper.mSToDate(row["timeOpen"]),
            lastUpdate=helper.mSToDate(row["lastUpdate"]),
            timeClose=helper.mSToDate(row["timeClose"]),
            duration=row["duration"],
            direction=TradeDirection(row['direction']),
            symbol=row["symbol"],
            chartLink=row["chartLink"],
            notes=row["notes"],
            notional=helper.check_decimal(row["notional"]),
            leverage=row["leverage"],
            avgOpenPrice=helper.check_decimal(row["avgOpenPrice"]),
            avgClosePrice=helper.check_decimal(row["avgClosePrice"]),
            riskAmt=helper.check_decimal(row["riskAmt"]),
            riskPercent=helper.check_decimal(row["riskPercent"]),
            accountBalance=helper.check_decimal(row["accountBalance"]),
            profit=helper.check_decimal(row["profit"]),
            fees=helper.check_decimal(row["fees"]),
            exchange_connection_id=int(row["exchange_connection_id"]),
            strategy=str(row["strategy"]) if row["strategy"] is not None else "",
            time_frame=str(row["time_frame"]) if row["time_frame"] is not None else "",
            source=TradeSource(row.get("source", "import")) if row.get("source") else TradeSource.IMPORT,
            username=row["username"]
        )

    @classmethod
    def from_bybit_position(cls, position, username):
        from services.bybit import Bybit

        # Get the account balance
        account_balance = Bybit.getAccountBalanceDecimal()

        # Determine direction based on position side
        direction = TradeDirection.LONG if position.side.lower() == "buy" else TradeDirection.SHORT

        # Create a new Trade instance
        return cls(
            id_field=None,
            exchange_trade_id=None,
            trade_orders=[],  # No orders initially, as we're creating directly from position
            unfilled_orders=[],
            symbol=position.symbol,
            timeOpen=position.created_time if position.created_time else position.updated_time,
            lastUpdate=position.updated_time if position.updated_time else None,
            timeClose=None,
            exchange_connection_id=exchange_connection_id,
            direction=direction,
            accountBalance=account_balance,
            status=TradeStatus.OPEN,
            tradeQty=position.qty,
            openQty=position.qty,
            duration=None,
            chartLink=None,
            notes=None,
            notional=position.cum_entry_value if position.cum_entry_value else position.qty * position.avg_entry_price,
            leverage=position.leverage,
            avgOpenPrice=position.avg_entry_price,
            avgClosePrice=None,
            riskAmt=Decimal(0),  # Will be calculated in updateTradeDetails
            riskPercent=Decimal(0),  # Will be calculated in updateTradeDetails
            profit=Decimal(0),  # Will be calculated in updateTradeDetails
            fees=Decimal(0),
            strategy=None,
            time_frame=None,
            source=TradeSource.IMPORT,
            username=username
        )

    @classmethod
    def from_fcm_position(cls, position, username):
        """
        Creates a Trade instance from a Coinbase FCMPosition object.
        
        Args:
            position: The FCMPosition object
            username: The username associated with this trade
        
        Returns:
            Trade: A new Trade instance representing the position
        """
        # Get the account balance
        account_balance = Decimal(0)  # Will be updated with actual balance later

        # Determine direction based on position side
        direction = TradeDirection.LONG if position.side == "LONG" else TradeDirection.SHORT

        # Create a new Trade instance
        return cls(
            id_field=None,
            exchange_trade_id=None,
            trade_orders=[],  # No orders initially, as we're creating directly from position
            unfilled_orders=[],
            symbol=position.product_id,
            timeOpen=position.expiration_time,  # Using expiration_time as a reference point
            lastUpdate=datetime.now(timezone.utc),
            timeClose=None,
            exchange_connection_id=exchange_connection_id,
            direction=direction,
            accountBalance=account_balance,
            status=TradeStatus.OPEN,
            tradeQty=position.number_of_contracts,
            openQty=position.number_of_contracts,
            duration=None,
            chartLink=None,
            notes=None,
            notional=position.number_of_contracts * position.avg_entry_price,
            leverage=None,
            avgOpenPrice=position.avg_entry_price,
            avgClosePrice=None,
            riskAmt=Decimal(0),  # Will be calculated in updateTradeDetails
            riskPercent=Decimal(0),  # Will be calculated in updateTradeDetails
            profit=position.unrealized_pnl,
            fees=Decimal(0),
            strategy=None,
            time_frame=None,
            source=TradeSource.IMPORT,
            username=username
        )

    def sortOrders(self, reverse=False):
        # Sort trade_orders by fill_date (or created_date if fill_date is None) with oldest(opening order) first
        self.trade_orders.sort(key=lambda od: od.filled_date if od.filled_date else od.created_date, reverse=reverse)
        self.unfilled_orders.sort(key=lambda od: od.filled_date if od.filled_date else od.created_date, reverse=reverse)

    def update_with_order(self, order, live_position):
        # order.trade_id = self.id_field
        self.trade_orders.append(order)
        self.sortOrders()
        return self.update_trade_details(live_position)

    def update_trade_details(self, live_position=False, save=False):
        self.tradeQty = self.getTotalQuantity()
        self.openQty = self.get_open_quantity()
        self.lastUpdate = self.getLastUpdateTime()
        self.timeOpen = self.getOpenTime()

        if self.status == TradeStatus.SETUP:
            print("[DEBUG] Trade is in SETUP status, skipping update_trade_details")
            self.profit = self.getProfit()
        elif self.openQty == 0 and live_position is False:
            self.status = TradeStatus.CLOSED
            self.avgClosePrice = self.getAvgClosePrice()
            self.timeClose = self.getClosedTime()
            self.duration = helper.calculate_duration_seconds(time_open=self.timeOpen,
                                                              time_close=self.timeClose)
            self.profit = self.getProfit()
        else:
            # Trade is still open
            self.status = TradeStatus.OPEN
            # Calculate unrealized profit based on current market price for open positions
            # self.profit = self.getProfit()
            # Set time_close to now
            self.duration = helper.calculate_duration_seconds(time_open=self.timeOpen,
                                                              time_close=helper.get_now_date())

        self.chartLink = None
        self.notes = None
        self.notional = self.getNotional()
        self.leverage = None
        self.avgOpenPrice = self.getAvgOpenPrice()
        self.riskAmt = self.getRiskAmount()
        self.riskPercent = self.getRiskPercent()
        self.fees = self.getTotalFees()
        # Set self.trade_id to the first(opening) order_id in trade_orders
        self.exchange_trade_id = self.trade_orders[0].order_id

        if save:
            import threading
            def save_to_db():
                from trades_db import TradesDB
                connection = trades_db.get_db_connection()
                cursor = connection.cursor()
                try:
                    # Begin transaction
                    connection.execute("BEGIN TRANSACTION")
                    TradesDB.updateTrade(self, cursor)
                    connection.execute("COMMIT")
                except Exception as e:
                    # Rollback on error
                    connection.execute("ROLLBACK")
                    print(f"❌ Error in update_trade_details: {str(e)}", file=sys.stderr)
                    traceback.print_exc()
                    raise

            # Run DB update in background thread
            threading.Thread(target=save_to_db, daemon=True).start()
        return self.get_extra_quantity() if self.exchange == Exchange.COINBASE else Decimal(0)

    def calculateRisk(self):
        self.riskAmt = self.getRiskAmount()
        self.riskPercent = self.getRiskPercent()

    def setExchange(self):
        if self.trade_orders[0].sierraActivity is not None:
            return Exchange.EDGECME
        elif self.trade_orders[0].coinbaseOrder is not None:
            return Exchange.COINBASE
        else:
            return Exchange.BYBIT

    def notionalString(self):
        return '{0:.2f}'.format(self.notional) if self.notional is not None else "$0.00"

    def profitString(self):
        return '{0:.2f}'.format(self.profit) if self.profit is not None else "$0.00"

    def feesString(self):
        return '{0:.2f}'.format(self.fees) if self.fees is not None else "$0.00"

    def getTradeDurationString(self):
        if self.duration is None:
            return "Open"
        days = self.duration // (24 * 3600)
        remaining_seconds = self.duration % (24 * 3600)
        hours = remaining_seconds // 3600
        remaining_seconds %= 3600
        minutes = remaining_seconds // 60
        seconds = remaining_seconds % 60
        if int(days) > 0:
            tradeDuration = f"{int(days)}d:{int(hours)}h:{int(minutes)}m"
        elif int(hours) > 0:
            tradeDuration = f"{int(hours)}h:{int(minutes)}m"
        else:
            tradeDuration = f"{int(minutes)}m"
        return tradeDuration

    def getProfit(self):
        """
        Calculate the profit for this trade.

        For open trades, uses the current market price.
        For closed trades, uses the average close price.

        Returns:
            Decimal: The calculated profit
        """
        if self.status == TradeStatus.OPEN:
            qty = self.get_open_quantity()
            # For open trades, get the current market price
            avg_close = self.get_current_price()
        elif self.status == TradeStatus.CLOSED:
            qty = self.getTotalQuantity()
            # For closed trades, use the average close price
            avg_close = self.getAvgClosePrice()
        elif self.status == TradeStatus.EXPIRED:
            qty = self.getTotalQuantity()
            # For EXPIRED trades, use the straight price at expiration (no orders to close)
            avg_close = self.avgClosePrice
        elif self.status == TradeStatus.SETUP:
            profit = self.getTakeProfitAmount()
            total_fees = Decimal(0)
            # Round to 2 decimal places
            return (profit - total_fees).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
        else:
            return Decimal(0)

        # Get the average entry price
        avg_entry = self.getAvgOpenPrice()

        # Calculate profit based on direction
        if self.direction == TradeDirection.LONG:
            profit = (avg_close * qty) - (avg_entry * qty)
        else:  # SHORT
            profit = (avg_entry * qty) - (avg_close * qty)

        # Apply contract multiplier (for futures contracts)
        multiplier = self.get_contract_multiplier()
        profit = profit * multiplier

        # Subtract fees
        total_fees = sum(order.fee for order in self.trade_orders)

        # Round to 2 decimal places
        return (profit - total_fees).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    def calculate_profit(self):
        self.profit = self.getProfit()

    def get_contract_multiplier(self):
        if self.exchange == Exchange.COINBASE:
            if "-CDE" in self.symbol:
                print(f"[DEBUG] Getting contract multiplier for {self.symbol}")
                product = services.coinbase_advanced.get_product(symbol=self.symbol)
                return Decimal(product["future_product_details"]["contract_size"])
            else:
                return Decimal(1)
        elif self.exchange == Exchange.EDGECME:
            return Decimal(1)
        else:
            return Decimal(1)

    def get_current_price(self):
        """
        Gets the current market price for the trade's symbol.

        This is used to calculate unrealized profit for open positions.

        Returns:
            Decimal: The current market price
        """
        try:
            if self.exchange == Exchange.EDGECME:
                # For EDGECME, we don't have real-time prices, so use the average open price
                # print(f"[DEBUG] {self.symbol}: Using avgOpenPrice for EDGECME: {self.avgOpenPrice}")
                return self.avgOpenPrice
            elif self.exchange == Exchange.COINBASE:
                # For Coinbase, get the current price from the API
                product = services.coinbase_advanced.get_current_product_price(self.symbol)
                if product:
                    current_price = Decimal(product["price"])
                    # print(f"[DEBUG] {self.symbol}: Coinbase API returned price: {current_price} (avgOpen: {self.avgOpenPrice})")
                    return current_price
                else:
                    # If we can't get the current price, fall back to the average open price
                    # print(f"[DEBUG] {self.symbol}: No product data from Coinbase, using avgOpenPrice: {self.avgOpenPrice}")
                    return self.avgOpenPrice
            elif self.exchange == Exchange.BYBIT:
                # For Bybit, get the current price from the API
                from services.bybit import Bybit
                price = Bybit.getPrice(self.symbol)
                if price:
                    current_price = Decimal(str(price))
                    # print(f"[DEBUG] {self.symbol}: Bybit API returned price: {current_price} (avgOpen: {self.avgOpenPrice})")
                    return current_price
                else:
                    # If we can't get the current price, fall back to the average open price
                    # print(f"[DEBUG] {self.symbol}: No price from Bybit, using avgOpenPrice: {self.avgOpenPrice}")
                    return self.avgOpenPrice
            else:
                # Default fallback for unknown exchanges
                # print(f"[DEBUG] {self.symbol}: Unknown exchange {self.exchange}, using avgOpenPrice: {self.avgOpenPrice}")
                return self.avgOpenPrice
        except Exception as e:
            # If there's any error getting the current price, log it and fall back to the average open price
            print(f"❌ [DEBUG] Error getting current price for {self.symbol}: {str(e)}, using avgOpenPrice: {self.avgOpenPrice}", file=sys.stderr)
            traceback.print_exc()
            return self.avgOpenPrice

    def getTotalFees(self):
        fees = Decimal(0)
        filled_orders = [order for order in self.trade_orders if order.filled]
        for order in filled_orders:
            fees += order.fee

        return fees.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

    def _calculate_order_amount(self, is_risk_calculation=True):
        """
        Helper method to calculate risk or profit amount based on stop loss or take profit orders.
        
        Args:
            is_risk_calculation (bool): If True, calculates risk amount from stop loss orders.
                                       If False, calculates profit amount from take profit orders.
        
        Returns:
            Decimal: The calculated amount (risk or profit), rounded to 2 decimal places
        """
        # Initialize variables
        total_qty = Decimal(0)
        weighted_price = Decimal(0)

        # Get the average entry price
        avg_entry = self.getAvgOpenPrice()

        # Find relevant orders based on a calculation type
        relevant_orders = []

        if self.exchange == Exchange.COINBASE:
            if is_risk_calculation:
                # For risk, look for Stop orders
                relevant_orders = [order for order in self.trade_orders
                                   if order.orderType == "Stop" and order.orderStatus == "Open"]
            else:
                # For profit, look for Limit orders that are reduce-only
                relevant_orders = [order for order in self.trade_orders
                                   if order.orderType == "Limit" and order.orderStatus == "Open" and order.reduce]
        else:
            # For other exchanges, identify orders based on price relative to entry
            if self.direction == TradeDirection.LONG:
                if is_risk_calculation:
                    # For LONG risk, stop loss orders have price < entry price
                    relevant_orders = [order for order in self.trade_orders
                                       if order.reduce and order.price < avg_entry and order.orderStatus == "Open"]
                else:
                    # For LONG profit, take profit orders have price > entry price
                    relevant_orders = [order for order in self.trade_orders
                                       if order.reduce and order.price > avg_entry]
            else:  # SHORT
                if is_risk_calculation:
                    # For SHORT risk, stop loss orders have price > entry price
                    relevant_orders = [order for order in self.trade_orders
                                       if order.reduce and order.price > avg_entry]
                else:
                    # For SHORT profit, take profit orders have price < entry price
                    relevant_orders = [order for order in self.trade_orders
                                       if order.reduce and order.price < avg_entry]

        # If no relevant orders found, return 0
        if not relevant_orders:
            return Decimal(0)

        # Calculate weighted average price
        for order in relevant_orders:
            total_qty += order.quantity
            weighted_price += order.price * order.quantity

        # If we have orders but no quantity, return 0
        if total_qty == 0:
            return Decimal(0)

        # Calculate the weighted average price
        avg_order_price = weighted_price / total_qty

        # Get the position quantity
        if self.status == TradeStatus.OPEN:
            qty = self.get_open_quantity()
        else:
            qty = self.getTotalQuantity()

        # Calculate amount based on direction and calculation type
        if self.direction == TradeDirection.LONG:
            if is_risk_calculation:
                # For LONG risk: entry - stop
                amount = (avg_entry - avg_order_price) * min(qty, total_qty)
            else:
                # For LONG profit: take profit - entry
                amount = (avg_order_price - avg_entry) * min(qty, total_qty)
        else:  # SHORT
            if is_risk_calculation:
                # For SHORT risk: stop - entry
                amount = (avg_order_price - avg_entry) * min(qty, total_qty)
            else:
                # For SHORT profit: entry - take profit
                amount = (avg_entry - avg_order_price) * min(qty, total_qty)

        # Ensure we don't return negative values
        amount = max(amount, Decimal(0))

        # Apply contract multiplier
        amount = amount * self.get_contract_multiplier()

        # Round to 2 decimal places
        return amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    def getRiskAmount(self):
        """
        Calculate the risk amount for this trade based on stop loss orders.
        
        Identifies stop loss orders based on price relative to entry:
        - For LONG positions: reduce orders with price < entry price
        - For SHORT positions: reduce orders with price > entry price
        
        Returns:
            Decimal: The calculated risk amount, rounded to 2 decimal places
        """
        return self._calculate_order_amount(is_risk_calculation=True)

    def getTakeProfitAmount(self):
        """
        Calculate the potential profit amount for this trade based on take profit orders.
        
        Identifies take profit orders based on price relative to entry:
        - For LONG positions: reduce orders with price > entry price
        - For SHORT positions: reduce orders with price < entry price
        
        Returns:
            Decimal: The calculated potential profit amount, rounded to 2 decimal places
        """
        return self._calculate_order_amount(is_risk_calculation=False)

    def getRiskPercent(self):
        if self.riskAmt > 0 and self.accountBalance > 0:
            risk_percent = (self.riskAmt / self.accountBalance) * 100
            return risk_percent.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        return Decimal(0).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    def getNotional(self):
        buy_notional = Decimal(0)
        sell_notional = Decimal(0)
        for order in self.trade_orders:
            # Use quantity instead of filledQuantity when trade status is SETUP
            qty_to_use = order.quantity if self.status == TradeStatus.SETUP else order.filledQuantity
            price_to_use = order.price if self.status == TradeStatus.SETUP else order.fillPrice
            if order.buySell == BuySell.BUY:
                buy_notional += price_to_use * qty_to_use
            elif order.buySell == BuySell.SELL:
                sell_notional += price_to_use * qty_to_use

        notional = max(buy_notional, sell_notional)
        notional = notional * self.get_contract_multiplier()

        return notional.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    def getTotalQuantity(self):
        buy_qty = Decimal(0)
        sell_qty = Decimal(0)
        for order in self.trade_orders:
            # Use quantity instead of filledQuantity when trade status is SETUP
            qty_to_use = order.quantity if self.status == TradeStatus.SETUP else order.filledQuantity

            if order.buySell == BuySell.BUY:
                buy_qty += qty_to_use
            else:
                sell_qty += qty_to_use

        return max(buy_qty, sell_qty)

    # Returns quantity
    def get_open_quantity(self):
        """
        Calculate the open quantity for this trade.

        For LONG trades: buy_qty - sell_qty
        For SHORT trades: sell_qty - buy_qty

        Returns:
            Decimal: The open quantity (always positive or zero)
        """
        buy_qty = Decimal(0)
        sell_qty = Decimal(0)

        # Sum up all buy and sell quantities
        for order in self.trade_orders:
            if order.buySell == BuySell.BUY:
                buy_qty += order.filledQuantity
            else:
                sell_qty += order.filledQuantity

        # Calculate open quantity based on direction
        if self.direction == TradeDirection.LONG:
            open_qty = buy_qty - sell_qty
            return open_qty
        else:  # SHORT
            open_qty = sell_qty - buy_qty
            return open_qty

    # Returns Extra Quantity for conversion orders
    def get_extra_quantity(self):
        """
        Calculate the extra quantity for this trade.

        This is used to detect position flips (e.g., when a LONG position becomes a SHORT position).
        Unlike get_open_quantity, this can return negative values.

        Returns:
            Decimal: The extra quantity (can be negative)
        """
        buy_qty = Decimal(0)
        sell_qty = Decimal(0)

        # Sum up all buy and sell quantities
        for order in self.trade_orders:
            if order.buySell == BuySell.BUY:
                buy_qty += order.filledQuantity
            else:
                sell_qty += order.filledQuantity

        # Calculate extra quantity based on direction
        if self.direction == TradeDirection.LONG:
            return buy_qty - sell_qty
        else:  # SHORT
            return sell_qty - buy_qty

    def getOpenTime(self):
        open_time = self.trade_orders[0].filled_date if self.trade_orders[0].filled_date is not None \
            else self.trade_orders[0].created_date
        if open_time is None:
            return self.timeOpen
        return open_time

    def getOpenPrice(self):
        return self.trade_orders[0].fillPrice

    def getClosedTime(self):
        closed_time = self.trade_orders[-1].filled_date if self.trade_orders[-1].filled_date is not None \
            else self.trade_orders[-1].created_date
        if closed_time is None:
            return self.timeClose
        return closed_time

    def getClosePrice(self):
        return self.trade_orders[-1].fillPrice

    def getLastUpdateTime(self):
        if not self.trade_orders:
            return self.lastUpdate  # or raise an appropriate error or return datetime.min
        last_order = self.trade_orders[-1]
        return max(filter(None, [last_order.filled_date, last_order.created_date]))

    def getAvgOpenPrice(self):
        return self.getAvgPrice(for_close=False)

    def getAvgClosePrice(self):
        return self.getAvgPrice(for_close=True)

    @staticmethod
    def apply_adaptive_precision(value: Decimal) -> Decimal:
        """
        Apply adaptive precision based on the value's magnitude.

        Args:
            value: The decimal value to apply precision to

        Returns:
            Decimal: The value with appropriate precision applied
        """
        if value >= Decimal('1000'):
            # For large values (≥1000), use 2 decimal places
            return value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        elif value >= Decimal('1'):
            # For medium values (1-999.99), use 4 decimal places
            return value.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
        elif value >= Decimal('0.01'):
            # For small values (0.01-0.9999), use 6 decimal places
            return value.quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP)
        else:
            # For very small values (<0.01), use 8 decimal places
            return value.quantize(Decimal('0.00000001'), rounding=ROUND_HALF_UP)

    def getAvgPrice(self, for_close: bool):
        total_price = Decimal(0)
        total_quantity = Decimal(0)

        # Determine the filter condition based on for_close and trade direction
        if for_close:
            # For LONG trades, average the SELL orders; for SHORT trades, average the BUY orders
            filter_condition = lambda od: (
                    (self.direction == TradeDirection.LONG and od.buySell == BuySell.SELL) or
                    (self.direction == TradeDirection.SHORT and od.buySell == BuySell.BUY)
            )
        else:
            # For LONG trades, average the BUY orders; for SHORT trades, average the SELL orders
            filter_condition = lambda od: (
                    (self.direction == TradeDirection.LONG and od.buySell == BuySell.BUY) or
                    (self.direction == TradeDirection.SHORT and od.buySell == BuySell.SELL)
            )

        for order in self.trade_orders:
            qty_to_use = order.quantity if self.status == TradeStatus.SETUP else order.filledQuantity
            price_to_use = order.price if self.status == TradeStatus.SETUP else order.fillPrice

            if filter_condition(order):
                total_price += price_to_use * qty_to_use
                total_quantity += qty_to_use

        # Calculate the average price
        if total_quantity > 0:
            value = total_price / total_quantity
            # Apply adaptive precision using the extracted method
            return self.apply_adaptive_precision(value)
        else:
            return Decimal(0)  # Return 0 if no matching orders

    # We are missing the opening order for this trade during initial import (2 year api limit)
    def close_imported_trade(self):
        self.update_trade_details()
        self.status = TradeStatus.CLOSED
        self.timeClose = self.getOpenTime()
        self.duration = 0
        self.profit = Decimal(0)
        self.openQty = Decimal(0)
        self.avgClosePrice = self.getAvgClosePrice()
        now_str = helper.formatDate(helper.get_now_date())
        self.notes = f"[{now_str}] Trade closed during import, this is probably due to the ByBit 2 Year API limit. The opening orders for this trade are unavailable via API."

    # We are missing the opening order for this trade during initial import (2 year api limit)
    def close_overflow_trade(self):
        self.update_trade_details()
        self.status = TradeStatus.CLOSED
        self.timeClose = self.getClosedTime()
        self.duration = helper.calculate_duration_seconds(time_open=self.timeOpen,
                                                          time_close=self.timeClose)
        self.openQty = Decimal(0)
        self.avgClosePrice = self.getAvgClosePrice()
        self.profit = self.getProfit()

        now_str = helper.formatDate(helper.get_now_date())
        self.notes = f"[{now_str}] Trade closed during import, this is probably due to the ByBit 2 Year API limit. The opening orders for this trade are unavailable via API."

    # We are closing an expired Coinbase Future position (API does not return closing orders for expired positions)
    def close_expired_coinbase_trade(self, expiration_date):
        self.update_trade_details()
        self.status = TradeStatus.EXPIRED
        self.timeClose = expiration_date
        self.duration = helper.calculate_duration_seconds(time_open=self.timeOpen,
                                                          time_close=self.timeClose)
        self.openQty = Decimal(0)
        self.avgClosePrice = self.get_price_at_expiration(expiration_date)
        self.profit = self.getProfit()
        print(f"Closing expired Coinbase trade: {self.symbol} at {expiration_date}")

        now_str = helper.formatDate(helper.get_now_date())
        self.notes = f"[{now_str}] Trade closed during import, this is probably due to the Coinbase Future position expiring. The closing orders for this trade are unavailable via API."

    def get_price_at_expiration(self, expiration_date):
        """
        Get the asset price at expiration using Binance API.
        Expiration time is 11AM ET on the expiration date.

        Returns:
            Decimal: The price at expiration, or fallback to getAvgClosePrice()
        """
        try:
            # Parse the base symbol from futures format (e.g., "SHB-30MAY25-CDE" -> "SHB")
            base_symbol = self.symbol.split('-')[0]
            # Create a mapping if SHB then use SHIB and if ET use ETH
            if base_symbol == "SHB":
                base_symbol = "SHIB"
            elif base_symbol == "ET":
                base_symbol = "ETH"
            elif base_symbol == "BIT":
                base_symbol = "BTC"
            elif base_symbol == "LNK":
                base_symbol = "LINK"
            elif base_symbol == "DOG":
                base_symbol = "DOGE"
            elif base_symbol == "LC":
                base_symbol = "LTC"

            # Use Binance API to get price at expiration (11AM ET)
            import services.binance as binance
            expiration_price = binance.get_price_at_expiration(base_symbol, expiration_date)

            if expiration_price is not None:
                return expiration_price
            else:
                print(f"Could not get expiration price for {self.symbol}, falling back to average close price")
                return self.getAvgClosePrice()
        except (ValueError, IndexError, Exception) as e:
            # Fallback to existing method if parsing or API call fails
            print(f"❌ Error getting expiration price for {self.symbol}: {str(e)}", file=sys.stderr)
            traceback.print_exc()
            return self.getAvgClosePrice()

    def __repr__(self):
        """
        Returns a comprehensive string representation of the Trade object.
        
        Returns:
            str: A formatted string with key trade details
        """
        # Format monetary values with 2 decimal places
        avg_open_price = f"${self.avgOpenPrice:.2f}" if self.avgOpenPrice else "N/A"
        avg_close_price = f"${self.avgClosePrice:.2f}" if self.avgClosePrice and self.status == TradeStatus.CLOSED else "N/A"
        profit_str = f"${self.profit:.2f}" if self.profit is not None else "$0.00"

        # Format quantity values
        qty_str = f"{self.tradeQty}" if self.tradeQty is not None else "0"
        open_qty_str = f"{self.openQty}" if self.openQty is not None else "0"

        # Build the representation string
        base_info = (
            f"{self.symbol} {self.direction.value} {self.status.name} "
            f"Open Qty: {open_qty_str}"
        )

        # Add additional details based on trade status
        if self.status == TradeStatus.CLOSED:
            return f"{base_info} → {avg_close_price}"
        else:
            return f"{base_info}"

    def to_dict(self):
        return {
            "symbol": self.symbol,
            "accountBalance": str(self.accountBalance),
            "exchange": self.exchange.value,
            "exchange_trade_id": self.exchange_trade_id,
            "direction": self.direction.value,
            "timeOpen": helper.date_to_ms(self.timeOpen),
            "status": self.status.value,
            "tradeQty": str(self.tradeQty),
            "openQty": str(self.openQty),
            "lastUpdate": helper.date_to_ms(self.lastUpdate),
            "timeClose": helper.date_to_ms(self.timeClose),
            "duration": self.duration,
            "chartLink": self.chartLink,
            "notes": self.notes,
            "notional": str(self.notional),
            "leverage": str(self.leverage),
            "avgOpenPrice": str(self.avgOpenPrice),
            "avgClosePrice": str(self.avgClosePrice),
            "riskAmt": str(self.riskAmt),
            "riskPercent": str(self.riskPercent),
            "profit": str(self.profit),
            "fees": str(self.fees),
            "strategy": self.strategy,
            "trade_orders": [order.to_dict() for order in self.trade_orders]
        }

    def to_json(self):
        return json.dumps(self.to_dict(), indent=4)


class Build(Enum):
    debug = 200
    build = 400


class TradeStatus(Enum):
    OPEN = "Open"
    CLOSED = "Closed"
    EXPIRED = "Expired"
    PENDING = "Pending"
    BUILDING = "Building"
    SKIPPED_CLOSURE = "Skipped Closure"
    SETUP = "Setup"
    UNFILLED = "Unfilled"


class TradeDirection(Enum):
    LONG = "Long"
    SHORT = "Short"
    UNKNOWN = "unknown"


class Exchange(Enum):
    UNKNOWN = "UNKNOWN"
    EDGECME = "CME-EdgeClear"
    BINANCE = "Binance"
    COINBASE = "Coinbase"
    KRAKEN = "Kraken"
    GEMINI = "Gemini"
    BITFINEX = "Bitfinex"
    HUOBI = "Huobi"
    KUCOIN = "KuCoin"
    BYBIT = "Bybit"
    OKX = "OKX"
    BITGET = "Bitget"
    BLOFIN = "Blofin"
